#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DNS记录导出脚本
导出所有域名的DNS记录到JSON文件
"""

import os
import sys
import json
import click
import time
from typing import Dict, Any, List

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from qcloud_api import QCloudDNSAPI
from utils import setup_logging, format_duration


class DNSExporter:
    """DNS导出器"""
    
    def __init__(self, output_file: str):
        """
        初始化DNS导出器
        
        Args:
            output_file: 输出文件路径
        """
        self.output_file = output_file
        self.logger = setup_logging()
        
        # 初始化腾讯云API
        try:
            self.api = QCloudDNSAPI()
            self.logger.info("腾讯云DNS API初始化成功")
        except Exception as e:
            self.logger.error(f"腾讯云DNS API初始化失败: {e}")
            sys.exit(1)
    
    def export_all_domains(self) -> Dict[str, Any]:
        """
        导出所有域名的DNS记录
        
        Returns:
            导出数据
        """
        export_data = {
            'export_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'domains': []
        }
        
        try:
            # 获取所有域名
            domains = self.api.get_domain_list()
            self.logger.info(f"发现 {len(domains)} 个域名")
            
            for domain_info in domains:
                domain_name = domain_info['name']
                self.logger.info(f"导出域名: {domain_name}")
                
                try:
                    # 获取域名的所有DNS记录
                    records = self.api.get_record_list(domain_name)
                    
                    domain_data = {
                        'domain': domain_name,
                        'domain_info': domain_info,
                        'records': records,
                        'record_count': len(records)
                    }
                    
                    export_data['domains'].append(domain_data)
                    self.logger.info(f"域名 {domain_name} 导出完成，共 {len(records)} 条记录")
                    
                    # 添加延迟避免API限制
                    time.sleep(0.5)
                    
                except Exception as e:
                    self.logger.error(f"导出域名 {domain_name} 失败: {e}")
                    domain_data = {
                        'domain': domain_name,
                        'domain_info': domain_info,
                        'error': str(e),
                        'records': [],
                        'record_count': 0
                    }
                    export_data['domains'].append(domain_data)
            
            export_data['total_domains'] = len(domains)
            export_data['total_records'] = sum(d.get('record_count', 0) for d in export_data['domains'])
            
            return export_data
            
        except Exception as e:
            self.logger.error(f"导出域名列表失败: {e}")
            raise
    
    def save_export_data(self, export_data: Dict[str, Any]) -> None:
        """
        保存导出数据到文件
        
        Args:
            export_data: 导出数据
        """
        try:
            # 创建输出目录
            output_dir = os.path.dirname(self.output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 保存数据
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"导出数据已保存到: {self.output_file}")
            
        except Exception as e:
            self.logger.error(f"保存导出数据失败: {e}")
            raise
    
    def run(self) -> bool:
        """
        执行DNS记录导出
        
        Returns:
            是否成功
        """
        start_time = time.time()
        
        self.logger.info("开始导出DNS记录")
        
        try:
            # 导出所有域名数据
            export_data = self.export_all_domains()
            
            # 保存到文件
            self.save_export_data(export_data)
            
            # 统计信息
            duration = time.time() - start_time
            total_domains = export_data.get('total_domains', 0)
            total_records = export_data.get('total_records', 0)
            
            self.logger.info(f"DNS记录导出完成，耗时: {format_duration(duration)}")
            self.logger.info(f"导出域名: {total_domains} 个")
            self.logger.info(f"导出记录: {total_records} 条")
            
            return True
            
        except Exception as e:
            self.logger.error(f"DNS记录导出失败: {e}")
            return False


@click.command()
@click.option('--output', '-o', required=True,
              help='输出文件路径')
@click.option('--log-level', '-l', default='INFO',
              type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']),
              help='日志级别')
def main(output: str, log_level: str):
    """
    DNS记录导出工具
    
    导出腾讯云账户下所有域名的DNS记录到JSON文件。
    """
    # 设置日志级别
    setup_logging(log_level)
    
    # 创建导出器并执行
    exporter = DNSExporter(output)
    success = exporter.run()
    
    # 设置退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
