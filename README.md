# 腾讯云DNS管理 GitLab CI项目

这个项目提供了一个基于GitLab CI/CD的自动化解决方案，用于管理腾讯云DNS解析记录。

## 功能特性

- 🚀 自动化DNS记录管理
- 📝 支持添加新的DNS解析记录
- ✏️ 支持修改现有DNS解析记录
- 🔒 安全的API密钥管理
- 📊 详细的操作日志
- 🔄 GitLab CI/CD集成

## 项目结构

```
qcloud_dns/
├── scripts/
│   ├── dns_manager.py          # DNS管理主脚本
│   ├── qcloud_api.py          # 腾讯云API封装
│   └── utils.py               # 工具函数
├── config/
│   ├── dns_config.yaml        # DNS配置文件
│   └── config_template.yaml   # 配置模板
├── .gitlab-ci.yml             # GitLab CI配置
├── requirements.txt           # Python依赖
└── README.md                  # 项目文档
```

## 快速开始

### 1. 环境准备

在GitLab项目的CI/CD设置中配置以下环境变量：

- `TENCENT_SECRET_ID`: 腾讯云API密钥ID
- `TENCENT_SECRET_KEY`: 腾讯云API密钥Key
- `TENCENT_REGION`: 腾讯云地域（默认：ap-beijing）

### 2. 配置DNS记录

复制 `config/config_template.yaml` 到 `config/dns_config.yaml` 并根据需要修改：

```yaml
domains:
  - domain: "example.com"
    records:
      - name: "www"
        type: "A"
        value: "***********"
        ttl: 600
      - name: "api"
        type: "CNAME"
        value: "api.example.com"
        ttl: 300
```

### 3. 触发CI/CD

提交代码到GitLab仓库，CI/CD流水线将自动执行DNS记录的更新。

## 支持的DNS记录类型

- A记录：将域名指向IPv4地址
- AAAA记录：将域名指向IPv6地址
- CNAME记录：将域名指向另一个域名
- MX记录：邮件交换记录
- TXT记录：文本记录
- NS记录：域名服务器记录

## API权限要求

确保腾讯云API密钥具有以下权限：
- DNS解析（DNSPod）的读写权限
- 域名管理权限

## 安全注意事项

1. 不要在代码中硬编码API密钥
2. 使用GitLab CI/CD变量存储敏感信息
3. 定期轮换API密钥
4. 限制API密钥的权限范围

## 故障排除

### 常见错误

1. **认证失败**: 检查API密钥是否正确配置
2. **域名不存在**: 确认域名已在腾讯云DNS中添加
3. **权限不足**: 检查API密钥权限设置

### 日志查看

在GitLab CI/CD的作业日志中可以查看详细的执行信息和错误提示。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
