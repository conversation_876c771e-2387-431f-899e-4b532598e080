#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
腾讯云DNS管理主脚本
支持批量添加、修改DNS记录
"""

import os
import sys
import json
import click
import time
from typing import List, Dict, Any, Optional
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from qcloud_api import QCloudDNSAPI
from utils import (
    setup_logging, load_config, validate_domain_config,
    format_duration, print_summary, create_backup_filename
)


class DNSManager:
    """DNS管理器"""
    
    def __init__(self, config_path: str = None, dry_run: bool = False):
        """
        初始化DNS管理器
        
        Args:
            config_path: 配置文件路径
            dry_run: 是否为试运行模式
        """
        self.config_path = config_path or "config/dns_config.yaml"
        self.dry_run = dry_run
        self.logger = setup_logging()
        
        # 初始化腾讯云API
        try:
            self.api = QCloudDNSAPI()
            self.logger.info("腾讯云DNS API初始化成功")
        except Exception as e:
            self.logger.error(f"腾讯云DNS API初始化失败: {e}")
            sys.exit(1)
        
        # 加载配置
        self.config = self.load_and_validate_config()
    
    def load_and_validate_config(self) -> Dict[str, Any]:
        """
        加载并验证配置文件
        
        Returns:
            配置字典
        """
        try:
            config = load_config(self.config_path)
            self.logger.info(f"配置文件加载成功: {self.config_path}")
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            sys.exit(1)
        
        # 验证配置
        errors = validate_domain_config(config)
        if errors:
            self.logger.error("配置文件验证失败:")
            for error in errors:
                self.logger.error(f"  - {error}")
            sys.exit(1)
        
        self.logger.info("配置文件验证通过")
        return config
    
    def backup_existing_records(self, domain: str) -> str:
        """
        备份现有DNS记录
        
        Args:
            domain: 域名
            
        Returns:
            备份文件路径
        """
        try:
            records = self.api.get_record_list(domain)
            backup_file = create_backup_filename(domain)
            backup_path = f"backups/{backup_file}"
            
            # 创建备份目录
            os.makedirs("backups", exist_ok=True)
            
            # 保存备份
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'domain': domain,
                    'backup_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'records': records
                }, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"域名 {domain} 的DNS记录已备份到: {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"备份DNS记录失败: {e}")
            return None
    
    def process_domain(self, domain_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        处理单个域名的DNS记录
        
        Args:
            domain_config: 域名配置
            
        Returns:
            操作结果列表
        """
        domain = domain_config['domain']
        records = domain_config['records']
        results = []
        
        self.logger.info(f"开始处理域名: {domain}")
        
        # 备份现有记录
        if not self.dry_run:
            self.backup_existing_records(domain)
        
        for record_config in records:
            result = self.process_record(domain, record_config)
            results.append(result)
            
            # 添加延迟避免API限制
            time.sleep(0.5)
        
        return results
    
    def process_record(self, domain: str, record_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单条DNS记录
        
        Args:
            domain: 域名
            record_config: 记录配置
            
        Returns:
            操作结果
        """
        name = record_config['name']
        record_type = record_config['type']
        value = record_config['value']
        ttl = record_config.get('ttl', 600)
        mx = record_config.get('mx')
        weight = record_config.get('weight')
        
        operation_info = f"{name}.{domain} ({record_type}) -> {value}"
        
        if self.dry_run:
            return {
                'success': True,
                'message': f"[试运行] 将处理记录: {operation_info}",
                'operation': 'dry_run'
            }
        
        try:
            # 查找现有记录
            existing_record = self.api.find_record(domain, name, record_type)
            
            if existing_record:
                # 修改现有记录
                self.logger.info(f"发现现有记录，准备修改: {operation_info}")
                result = self.api.modify_record(
                    domain=domain,
                    record_id=existing_record['record_id'],
                    name=name,
                    record_type=record_type,
                    value=value,
                    ttl=ttl,
                    mx=mx,
                    weight=weight
                )
                result['operation'] = 'modify'
            else:
                # 创建新记录
                self.logger.info(f"创建新记录: {operation_info}")
                result = self.api.create_record(
                    domain=domain,
                    name=name,
                    record_type=record_type,
                    value=value,
                    ttl=ttl,
                    mx=mx,
                    weight=weight
                )
                result['operation'] = 'create'
            
            result['record_info'] = operation_info
            return result
            
        except Exception as e:
            error_msg = f"处理记录失败 {operation_info}: {e}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'record_info': operation_info,
                'operation': 'error'
            }
    
    def run(self) -> bool:
        """
        执行DNS记录管理
        
        Returns:
            是否成功
        """
        start_time = time.time()
        all_results = []
        
        self.logger.info("开始执行DNS记录管理")
        
        if self.dry_run:
            self.logger.info("*** 试运行模式 - 不会实际修改DNS记录 ***")
        
        try:
            for domain_config in self.config['domains']:
                domain_results = self.process_domain(domain_config)
                all_results.extend(domain_results)
            
            # 打印结果摘要
            print_summary(all_results)
            
            # 统计信息
            duration = time.time() - start_time
            success_count = sum(1 for r in all_results if r.get('success', False))
            total_count = len(all_results)
            
            self.logger.info(f"DNS记录管理完成，耗时: {format_duration(duration)}")
            self.logger.info(f"成功: {success_count}/{total_count}")
            
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"DNS记录管理执行失败: {e}")
            return False


@click.command()
@click.option('--config', '-c', default='config/dns_config.yaml', 
              help='配置文件路径')
@click.option('--dry-run', '-d', is_flag=True, 
              help='试运行模式，不实际修改DNS记录')
@click.option('--log-level', '-l', default='INFO',
              type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']),
              help='日志级别')
def main(config: str, dry_run: bool, log_level: str):
    """
    腾讯云DNS记录管理工具
    
    支持批量添加和修改DNS记录，自动备份现有记录。
    """
    # 设置日志级别
    setup_logging(log_level)
    
    # 创建DNS管理器并执行
    manager = DNSManager(config, dry_run)
    success = manager.run()
    
    # 设置退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
