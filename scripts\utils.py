#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工具函数模块
提供日志配置、配置文件读取等通用功能
"""

import os
import sys
import yaml
import logging
import colorlog
from typing import Dict, Any, List
from datetime import datetime


def setup_logging(level: str = "INFO", log_file: str = None) -> logging.Logger:
    """
    配置日志系统
    
    Args:
        level: 日志级别
        log_file: 日志文件路径（可选）
        
    Returns:
        配置好的logger对象
    """
    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式化器
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 控制台处理器（带颜色）
    console_handler = colorlog.StreamHandler()
    console_formatter = colorlog.ColoredFormatter(
        '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_formatter = logging.Formatter(log_format, datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载YAML配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
        
    Raises:
        FileNotFoundError: 配置文件不存在
        yaml.YAMLError: YAML格式错误
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config or {}
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"配置文件格式错误: {e}")


def validate_dns_record(record: Dict[str, Any]) -> List[str]:
    """
    验证DNS记录配置
    
    Args:
        record: DNS记录配置
        
    Returns:
        错误信息列表，如果为空则验证通过
    """
    errors = []
    
    # 必需字段检查
    required_fields = ['name', 'type', 'value']
    for field in required_fields:
        if field not in record or not record[field]:
            errors.append(f"缺少必需字段: {field}")
    
    if errors:
        return errors
    
    # 记录类型检查
    valid_types = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'SRV', 'CAA']
    record_type = record['type'].upper()
    if record_type not in valid_types:
        errors.append(f"不支持的记录类型: {record_type}")
    
    # TTL检查
    if 'ttl' in record:
        ttl = record['ttl']
        if not isinstance(ttl, int) or ttl < 1 or ttl > 604800:
            errors.append(f"TTL值无效: {ttl}，应该在1-604800之间")
    
    # MX记录特殊检查
    if record_type == 'MX':
        if 'mx' not in record or not isinstance(record['mx'], int):
            errors.append("MX记录必须包含mx优先级字段")
        elif record['mx'] < 0 or record['mx'] > 65535:
            errors.append(f"MX优先级无效: {record['mx']}，应该在0-65535之间")
    
    # 权重检查
    if 'weight' in record:
        weight = record['weight']
        if not isinstance(weight, int) or weight < 0 or weight > 100:
            errors.append(f"权重值无效: {weight}，应该在0-100之间")
    
    return errors


def validate_domain_config(config: Dict[str, Any]) -> List[str]:
    """
    验证域名配置
    
    Args:
        config: 域名配置
        
    Returns:
        错误信息列表，如果为空则验证通过
    """
    errors = []
    
    if 'domains' not in config:
        errors.append("配置文件必须包含domains字段")
        return errors
    
    if not isinstance(config['domains'], list):
        errors.append("domains字段必须是列表")
        return errors
    
    for i, domain_config in enumerate(config['domains']):
        if not isinstance(domain_config, dict):
            errors.append(f"域名配置 {i} 必须是字典")
            continue
        
        if 'domain' not in domain_config:
            errors.append(f"域名配置 {i} 缺少domain字段")
            continue
        
        domain_name = domain_config['domain']
        
        if 'records' not in domain_config:
            errors.append(f"域名 {domain_name} 缺少records字段")
            continue
        
        if not isinstance(domain_config['records'], list):
            errors.append(f"域名 {domain_name} 的records字段必须是列表")
            continue
        
        for j, record in enumerate(domain_config['records']):
            record_errors = validate_dns_record(record)
            for error in record_errors:
                errors.append(f"域名 {domain_name} 记录 {j}: {error}")
    
    return errors


def format_duration(seconds: float) -> str:
    """
    格式化时间间隔
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化的时间字符串
    """
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def get_env_or_exit(env_name: str, description: str = None) -> str:
    """
    获取环境变量，如果不存在则退出程序
    
    Args:
        env_name: 环境变量名
        description: 环境变量描述
        
    Returns:
        环境变量值
    """
    value = os.getenv(env_name)
    if not value:
        desc = description or env_name
        print(f"错误: 环境变量 {env_name} 未设置 ({desc})", file=sys.stderr)
        sys.exit(1)
    return value


def safe_get(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    安全获取字典值
    
    Args:
        data: 字典
        key: 键名
        default: 默认值
        
    Returns:
        字典值或默认值
    """
    return data.get(key, default)


def print_summary(results: List[Dict[str, Any]]) -> None:
    """
    打印操作结果摘要
    
    Args:
        results: 操作结果列表
    """
    total = len(results)
    success = sum(1 for r in results if r.get('success', False))
    failed = total - success
    
    print(f"\n{'='*50}")
    print(f"操作完成摘要:")
    print(f"总计: {total}")
    print(f"成功: {success}")
    print(f"失败: {failed}")
    print(f"{'='*50}")
    
    if failed > 0:
        print("\n失败的操作:")
        for result in results:
            if not result.get('success', False):
                print(f"  - {result.get('message', '未知错误')}")


def create_backup_filename(domain: str) -> str:
    """
    创建备份文件名
    
    Args:
        domain: 域名
        
    Returns:
        备份文件名
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"dns_backup_{domain}_{timestamp}.json"
