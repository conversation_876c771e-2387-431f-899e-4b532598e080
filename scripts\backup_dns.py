#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DNS记录备份脚本
定期备份DNS记录
"""

import os
import sys
import json
import click
import time
from typing import Dict, Any, List

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from qcloud_api import QCloudDNSAPI
from utils import setup_logging, format_duration, create_backup_filename


class DNSBackup:
    """DNS备份管理器"""
    
    def __init__(self, backup_dir: str = "backups"):
        """
        初始化DNS备份管理器
        
        Args:
            backup_dir: 备份目录
        """
        self.backup_dir = backup_dir
        self.logger = setup_logging()
        
        # 创建备份目录
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # 初始化腾讯云API
        try:
            self.api = QCloudDNSAPI()
            self.logger.info("腾讯云DNS API初始化成功")
        except Exception as e:
            self.logger.error(f"腾讯云DNS API初始化失败: {e}")
            sys.exit(1)
    
    def backup_domain(self, domain: str) -> str:
        """
        备份单个域名的DNS记录
        
        Args:
            domain: 域名
            
        Returns:
            备份文件路径
        """
        try:
            # 获取DNS记录
            records = self.api.get_record_list(domain)
            
            # 创建备份文件名
            backup_file = create_backup_filename(domain)
            backup_path = os.path.join(self.backup_dir, backup_file)
            
            # 备份数据
            backup_data = {
                'domain': domain,
                'backup_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'records': records,
                'record_count': len(records)
            }
            
            # 保存备份
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"域名 {domain} 备份完成: {backup_path} ({len(records)} 条记录)")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"备份域名 {domain} 失败: {e}")
            return None
    
    def backup_all_domains(self) -> List[str]:
        """
        备份所有域名的DNS记录
        
        Returns:
            备份文件路径列表
        """
        backup_files = []
        
        try:
            # 获取所有域名
            domains = self.api.get_domain_list()
            self.logger.info(f"发现 {len(domains)} 个域名，开始备份")
            
            for domain_info in domains:
                domain_name = domain_info['name']
                backup_file = self.backup_domain(domain_name)
                
                if backup_file:
                    backup_files.append(backup_file)
                
                # 添加延迟避免API限制
                time.sleep(0.5)
            
            return backup_files
            
        except Exception as e:
            self.logger.error(f"获取域名列表失败: {e}")
            return backup_files
    
    def cleanup_old_backups(self, keep_days: int = 30) -> None:
        """
        清理旧的备份文件
        
        Args:
            keep_days: 保留天数
        """
        try:
            current_time = time.time()
            cutoff_time = current_time - (keep_days * 24 * 3600)
            
            deleted_count = 0
            
            for filename in os.listdir(self.backup_dir):
                if filename.startswith('dns_backup_') and filename.endswith('.json'):
                    file_path = os.path.join(self.backup_dir, filename)
                    file_mtime = os.path.getmtime(file_path)
                    
                    if file_mtime < cutoff_time:
                        try:
                            os.remove(file_path)
                            deleted_count += 1
                            self.logger.debug(f"删除旧备份文件: {filename}")
                        except Exception as e:
                            self.logger.warning(f"删除备份文件失败 {filename}: {e}")
            
            if deleted_count > 0:
                self.logger.info(f"清理了 {deleted_count} 个超过 {keep_days} 天的旧备份文件")
            else:
                self.logger.info("没有需要清理的旧备份文件")
                
        except Exception as e:
            self.logger.error(f"清理旧备份文件失败: {e}")
    
    def run(self, domains: List[str] = None, all_domains: bool = False, 
            cleanup_days: int = 30) -> bool:
        """
        执行DNS记录备份
        
        Args:
            domains: 指定要备份的域名列表
            all_domains: 是否备份所有域名
            cleanup_days: 清理旧备份的天数
            
        Returns:
            是否成功
        """
        start_time = time.time()
        backup_files = []
        
        self.logger.info("开始执行DNS记录备份")
        
        try:
            if all_domains:
                # 备份所有域名
                backup_files = self.backup_all_domains()
            elif domains:
                # 备份指定域名
                for domain in domains:
                    backup_file = self.backup_domain(domain)
                    if backup_file:
                        backup_files.append(backup_file)
                    time.sleep(0.5)
            else:
                self.logger.error("请指定要备份的域名或使用 --all-domains 备份所有域名")
                return False
            
            # 清理旧备份
            if cleanup_days > 0:
                self.cleanup_old_backups(cleanup_days)
            
            # 统计信息
            duration = time.time() - start_time
            success_count = len(backup_files)
            
            self.logger.info(f"DNS记录备份完成，耗时: {format_duration(duration)}")
            self.logger.info(f"成功备份: {success_count} 个域名")
            
            if backup_files:
                self.logger.info("备份文件:")
                for backup_file in backup_files:
                    self.logger.info(f"  - {backup_file}")
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"DNS记录备份执行失败: {e}")
            return False


@click.command()
@click.option('--domains', '-d', multiple=True,
              help='要备份的域名（可多次指定）')
@click.option('--all-domains', '-a', is_flag=True,
              help='备份所有域名')
@click.option('--backup-dir', '-b', default='backups',
              help='备份目录')
@click.option('--cleanup-days', '-c', default=30, type=int,
              help='清理多少天前的旧备份文件（0表示不清理）')
@click.option('--log-level', '-l', default='INFO',
              type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']),
              help='日志级别')
def main(domains: tuple, all_domains: bool, backup_dir: str, 
         cleanup_days: int, log_level: str):
    """
    DNS记录备份工具
    
    备份指定域名或所有域名的DNS记录到JSON文件。
    """
    # 设置日志级别
    setup_logging(log_level)
    
    # 创建备份管理器并执行
    backup = DNSBackup(backup_dir)
    success = backup.run(list(domains), all_domains, cleanup_days)
    
    # 设置退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
