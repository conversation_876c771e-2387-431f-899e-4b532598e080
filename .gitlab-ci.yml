# 腾讯云DNS管理 GitLab CI/CD 配置

# 定义阶段
stages:
  - validate
  - test
  - deploy-dns
  - cleanup

# 全局变量
variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  PYTHON_VERSION: "3.9"

# 缓存配置
cache:
  paths:
    - .cache/pip/
    - venv/

# 模板：Python环境准备
.python_template: &python_template
  image: python:${PYTHON_VERSION}-slim
  before_script:
    - apt-get update -qq && apt-get install -y -qq git
    - python -m venv venv
    - source venv/bin/activate
    - pip install --upgrade pip
    - pip install -r requirements.txt

# 阶段1：配置验证
validate_config:
  <<: *python_template
  stage: validate
  script:
    - echo "验证配置文件..."
    - python scripts/dns_manager.py --config config/dns_config.yaml --dry-run --log-level DEBUG
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  artifacts:
    reports:
      junit: test-results.xml
    expire_in: 1 week
    when: always

# 阶段2：单元测试
unit_tests:
  <<: *python_template
  stage: test
  script:
    - echo "运行单元测试..."
    - python -m pytest tests/ -v --junitxml=test-results.xml --cov=scripts --cov-report=xml --cov-report=term
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      junit: test-results.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    expire_in: 1 week
    when: always
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# 阶段3：DNS部署（开发环境）
deploy_dns_dev:
  <<: *python_template
  stage: deploy-dns
  environment:
    name: development
    url: https://dev.example.com
  script:
    - echo "部署DNS记录到开发环境..."
    - python scripts/dns_manager.py --config config/dns_config_dev.yaml --log-level INFO
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.*$/
      when: manual
  artifacts:
    paths:
      - backups/
    expire_in: 30 days

# 阶段3：DNS部署（生产环境）
deploy_dns_prod:
  <<: *python_template
  stage: deploy-dns
  environment:
    name: production
    url: https://example.com
  script:
    - echo "部署DNS记录到生产环境..."
    - python scripts/dns_manager.py --config config/dns_config.yaml --log-level INFO
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
    - if: $CI_COMMIT_TAG
  artifacts:
    paths:
      - backups/
    expire_in: 90 days
  dependencies:
    - validate_config
    - unit_tests

# 阶段4：清理
cleanup:
  image: alpine:latest
  stage: cleanup
  script:
    - echo "清理临时文件..."
    - find . -name "*.pyc" -delete
    - find . -name "__pycache__" -type d -exec rm -rf {} + || true
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  when: always

# 特殊作业：回滚DNS记录
rollback_dns:
  <<: *python_template
  stage: deploy-dns
  environment:
    name: production
    action: rollback
  script:
    - echo "回滚DNS记录..."
    - |
      if [ -z "$BACKUP_FILE" ]; then
        echo "错误: 请设置BACKUP_FILE变量指定要回滚的备份文件"
        exit 1
      fi
    - python scripts/rollback_dns.py --backup-file "$BACKUP_FILE" --log-level INFO
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      allow_failure: false
  dependencies: []

# 特殊作业：DNS记录导出
export_dns:
  <<: *python_template
  stage: deploy-dns
  script:
    - echo "导出DNS记录..."
    - python scripts/export_dns.py --output exports/dns_export_$(date +%Y%m%d_%H%M%S).json
  rules:
    - when: manual
  artifacts:
    paths:
      - exports/
    expire_in: 1 year

# 定时任务：DNS记录备份
scheduled_backup:
  <<: *python_template
  stage: deploy-dns
  script:
    - echo "执行定时DNS记录备份..."
    - python scripts/backup_dns.py --all-domains --log-level INFO
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
  artifacts:
    paths:
      - backups/
    expire_in: 180 days

# 安全扫描
security_scan:
  image: python:3.9-slim
  stage: test
  script:
    - pip install safety bandit
    - safety check --json --output safety-report.json || true
    - bandit -r scripts/ -f json -o bandit-report.json || true
  artifacts:
    reports:
      sast: bandit-report.json
    paths:
      - safety-report.json
      - bandit-report.json
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  allow_failure: true

# 代码质量检查
code_quality:
  image: python:3.9-slim
  stage: test
  script:
    - pip install flake8 pylint
    - flake8 scripts/ --output-file=flake8-report.txt --tee || true
    - pylint scripts/ --output-format=json > pylint-report.json || true
  artifacts:
    paths:
      - flake8-report.txt
      - pylint-report.json
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  allow_failure: true
