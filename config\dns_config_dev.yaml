# 开发环境DNS配置文件

domains:
  # 开发环境域名
  - domain: "dev.example.com"
    records:
      # 开发环境根域名
      - name: "@"
        type: "A"
        value: "192.168.1.200"
        ttl: 300
      
      # 开发环境API
      - name: "api"
        type: "A"
        value: "192.168.1.201"
        ttl: 300
      
      # 开发环境数据库
      - name: "db"
        type: "A"
        value: "192.168.1.202"
        ttl: 300
      
      # 测试子域名
      - name: "test"
        type: "A"
        value: "192.168.1.203"
        ttl: 300
