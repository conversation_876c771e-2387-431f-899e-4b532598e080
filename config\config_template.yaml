# 腾讯云DNS配置模板
# 复制此文件为 dns_config.yaml 并根据实际需求修改

# 域名配置列表
domains:
  # 示例域名1
  - domain: "example.com"
    records:
      # A记录 - 将域名指向IPv4地址
      - name: "www"
        type: "A"
        value: "***********00"
        ttl: 600
      
      # A记录 - 根域名
      - name: "@"
        type: "A"
        value: "***********00"
        ttl: 600
      
      # CNAME记录 - 将域名指向另一个域名
      - name: "api"
        type: "CNAME"
        value: "api-server.example.com"
        ttl: 300
      
      # CNAME记录 - CDN加速
      - name: "cdn"
        type: "CNAME"
        value: "cdn.example.com.cdn.dnsv1.com"
        ttl: 300
      
      # MX记录 - 邮件服务器
      - name: "@"
        type: "MX"
        value: "mail.example.com"
        ttl: 3600
        mx: 10  # MX优先级
      
      # TXT记录 - SPF记录
      - name: "@"
        type: "TXT"
        value: "v=spf1 include:_spf.example.com ~all"
        ttl: 3600
      
      # TXT记录 - 域名验证
      - name: "_verification"
        type: "TXT"
        value: "verification-token-12345"
        ttl: 300
      
      # AAAA记录 - IPv6地址
      - name: "ipv6"
        type: "AAAA"
        value: "2001:db8::1"
        ttl: 600
      
      # NS记录 - 子域名服务器
      - name: "subdomain"
        type: "NS"
        value: "ns1.subdomain.example.com"
        ttl: 86400

  # 示例域名2 - 开发环境
  - domain: "dev.example.com"
    records:
      - name: "@"
        type: "A"
        value: "*************"
        ttl: 300
      
      - name: "api"
        type: "A"
        value: "*************"
        ttl: 300
      
      - name: "db"
        type: "A"
        value: "*************"
        ttl: 300

# 配置说明：
# 
# domain: 域名（必需）
# records: DNS记录列表（必需）
#   - name: 记录名称（必需）
#     - "@" 表示根域名
#     - "www" 表示 www.example.com
#     - "*" 表示泛域名解析
#   
#   - type: 记录类型（必需）
#     - A: IPv4地址记录
#     - AAAA: IPv6地址记录
#     - CNAME: 别名记录
#     - MX: 邮件交换记录
#     - TXT: 文本记录
#     - NS: 域名服务器记录
#     - SRV: 服务记录
#     - CAA: 证书颁发机构授权记录
#   
#   - value: 记录值（必需）
#     - A记录: IPv4地址，如 "***********"
#     - AAAA记录: IPv6地址，如 "2001:db8::1"
#     - CNAME记录: 目标域名，如 "target.example.com"
#     - MX记录: 邮件服务器域名，如 "mail.example.com"
#     - TXT记录: 文本内容，如 "v=spf1 include:_spf.example.com ~all"
#     - NS记录: 域名服务器，如 "ns1.example.com"
#   
#   - ttl: 生存时间，单位秒（可选，默认600）
#     - 建议值：300-3600秒
#     - 频繁变更的记录使用较小值
#     - 稳定的记录使用较大值
#   
#   - mx: MX记录优先级（仅MX记录需要）
#     - 数值越小优先级越高
#     - 常用值：10, 20, 30
#   
#   - weight: 权重（可选）
#     - 用于负载均衡
#     - 范围：0-100

# 环境变量配置（在GitLab CI/CD中设置）：
# TENCENT_SECRET_ID: 腾讯云API密钥ID
# TENCENT_SECRET_KEY: 腾讯云API密钥Key
# TENCENT_REGION: 腾讯云地域（可选，默认ap-beijing）

# 常用记录类型示例：
# 
# 网站解析：
# - name: "@"
#   type: "A"
#   value: "服务器IP"
# 
# - name: "www"
#   type: "CNAME"
#   value: "example.com"
# 
# 邮件服务：
# - name: "@"
#   type: "MX"
#   value: "mail.example.com"
#   mx: 10
# 
# - name: "@"
#   type: "TXT"
#   value: "v=spf1 include:_spf.example.com ~all"
# 
# API服务：
# - name: "api"
#   type: "A"
#   value: "API服务器IP"
# 
# CDN加速：
# - name: "cdn"
#   type: "CNAME"
#   value: "CDN域名"
# 
# 域名验证：
# - name: "_verification"
#   type: "TXT"
#   value: "验证码"
