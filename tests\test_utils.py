#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工具函数测试
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch, mock_open

# 添加scripts目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'scripts'))

from utils import (
    validate_dns_record, validate_domain_config, 
    format_duration, safe_get
)


class TestUtils(unittest.TestCase):
    """工具函数测试类"""
    
    def test_validate_dns_record_valid(self):
        """测试有效DNS记录验证"""
        # 测试A记录
        record = {
            'name': 'www',
            'type': 'A',
            'value': '***********',
            'ttl': 600
        }
        errors = validate_dns_record(record)
        self.assertEqual(len(errors), 0)
        
        # 测试MX记录
        record = {
            'name': '@',
            'type': 'MX',
            'value': 'mail.example.com',
            'ttl': 3600,
            'mx': 10
        }
        errors = validate_dns_record(record)
        self.assertEqual(len(errors), 0)
    
    def test_validate_dns_record_invalid(self):
        """测试无效DNS记录验证"""
        # 缺少必需字段
        record = {
            'name': 'www',
            'type': 'A'
            # 缺少value字段
        }
        errors = validate_dns_record(record)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('value' in error for error in errors))
        
        # 无效记录类型
        record = {
            'name': 'www',
            'type': 'INVALID',
            'value': '***********'
        }
        errors = validate_dns_record(record)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('不支持的记录类型' in error for error in errors))
        
        # MX记录缺少mx字段
        record = {
            'name': '@',
            'type': 'MX',
            'value': 'mail.example.com'
            # 缺少mx字段
        }
        errors = validate_dns_record(record)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('mx优先级' in error for error in errors))
    
    def test_validate_domain_config_valid(self):
        """测试有效域名配置验证"""
        config = {
            'domains': [
                {
                    'domain': 'example.com',
                    'records': [
                        {
                            'name': 'www',
                            'type': 'A',
                            'value': '***********'
                        }
                    ]
                }
            ]
        }
        errors = validate_domain_config(config)
        self.assertEqual(len(errors), 0)
    
    def test_validate_domain_config_invalid(self):
        """测试无效域名配置验证"""
        # 缺少domains字段
        config = {}
        errors = validate_domain_config(config)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('domains字段' in error for error in errors))
        
        # domains不是列表
        config = {'domains': 'not_a_list'}
        errors = validate_domain_config(config)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('必须是列表' in error for error in errors))
    
    def test_format_duration(self):
        """测试时间格式化"""
        # 毫秒
        self.assertEqual(format_duration(0.5), '500ms')
        
        # 秒
        self.assertEqual(format_duration(1.5), '1.5s')
        
        # 分钟
        self.assertEqual(format_duration(90), '1.5m')
        
        # 小时
        self.assertEqual(format_duration(7200), '2.0h')
    
    def test_safe_get(self):
        """测试安全字典取值"""
        data = {'key1': 'value1', 'key2': None}
        
        # 存在的键
        self.assertEqual(safe_get(data, 'key1'), 'value1')
        
        # 值为None的键
        self.assertIsNone(safe_get(data, 'key2'))
        
        # 不存在的键，使用默认值
        self.assertEqual(safe_get(data, 'key3', 'default'), 'default')
        
        # 不存在的键，无默认值
        self.assertIsNone(safe_get(data, 'key3'))


if __name__ == '__main__':
    unittest.main()
