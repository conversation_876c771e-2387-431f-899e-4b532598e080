# 腾讯云DNS管理项目使用指南

## 项目概述

这个项目提供了一套完整的腾讯云DNS管理解决方案，支持通过GitLab CI/CD自动化管理DNS解析记录。

## 环境配置

### 1. GitLab CI/CD变量配置

在GitLab项目的 **Settings > CI/CD > Variables** 中添加以下环境变量：

| 变量名 | 描述 | 是否必需 | 示例值 |
|--------|------|----------|--------|
| `TENCENT_SECRET_ID` | 腾讯云API密钥ID | 是 | `AKIDxxxxxxxxxxxxxxxx` |
| `TENCENT_SECRET_KEY` | 腾讯云API密钥Key | 是 | `xxxxxxxxxxxxxxxx` |
| `TENCENT_REGION` | 腾讯云地域 | 否 | `ap-beijing` |

### 2. 腾讯云API密钥获取

1. 登录腾讯云控制台
2. 访问 [API密钥管理](https://console.cloud.tencent.com/cam/capi)
3. 创建新的API密钥或使用现有密钥
4. 确保密钥具有DNS解析（DNSPod）的读写权限

## 配置文件说明

### 主配置文件：`config/dns_config.yaml`

```yaml
domains:
  - domain: "example.com"
    records:
      - name: "www"
        type: "A"
        value: "***********00"
        ttl: 600
      - name: "api"
        type: "CNAME"
        value: "api-server.example.com"
        ttl: 300
```

### 开发环境配置：`config/dns_config_dev.yaml`

用于开发环境的DNS配置，通常包含测试域名和开发服务器地址。

## 使用方法

### 1. 本地测试

```bash
# 安装依赖
pip install -r requirements.txt

# 配置验证（试运行）
python scripts/dns_manager.py --config config/dns_config.yaml --dry-run

# 执行DNS更新
python scripts/dns_manager.py --config config/dns_config.yaml
```

### 2. GitLab CI/CD流水线

#### 自动触发场景

- **推送到develop分支**：自动部署到开发环境
- **推送到main分支**：需要手动确认部署到生产环境
- **创建标签**：自动部署到生产环境

#### 手动触发场景

- **功能分支**：可手动触发部署到开发环境
- **生产部署**：需要手动确认
- **DNS回滚**：需要手动触发并指定备份文件

### 3. 常用操作

#### DNS记录备份

```bash
# 备份所有域名
python scripts/backup_dns.py --all-domains

# 备份指定域名
python scripts/backup_dns.py --domains example.com --domains test.com
```

#### DNS记录导出

```bash
# 导出所有DNS记录
python scripts/export_dns.py --output exports/dns_export.json
```

#### DNS记录回滚

```bash
# 从备份文件回滚
python scripts/rollback_dns.py --backup-file backups/dns_backup_example.com_20231201_120000.json
```

## CI/CD流水线说明

### 阶段说明

1. **validate**：配置文件验证和语法检查
2. **test**：单元测试和代码质量检查
3. **deploy-dns**：DNS记录部署
4. **cleanup**：清理临时文件

### 作业说明

| 作业名 | 阶段 | 触发条件 | 描述 |
|--------|------|----------|------|
| `validate_config` | validate | 所有推送和MR | 验证配置文件格式 |
| `unit_tests` | test | 所有推送和MR | 运行单元测试 |
| `deploy_dns_dev` | deploy-dns | develop分支 | 部署到开发环境 |
| `deploy_dns_prod` | deploy-dns | main分支（手动） | 部署到生产环境 |
| `rollback_dns` | deploy-dns | 手动触发 | 回滚DNS记录 |
| `export_dns` | deploy-dns | 手动触发 | 导出DNS记录 |
| `scheduled_backup` | deploy-dns | 定时任务 | 定时备份 |

## 最佳实践

### 1. 配置管理

- 使用版本控制管理配置文件
- 为不同环境创建不同的配置文件
- 定期备份DNS记录

### 2. 安全考虑

- 不要在代码中硬编码API密钥
- 定期轮换API密钥
- 限制API密钥权限范围
- 使用GitLab CI/CD变量存储敏感信息

### 3. 部署策略

- 先在开发环境测试
- 使用试运行模式验证配置
- 生产环境部署前进行备份
- 保留多个版本的备份文件

### 4. 监控和日志

- 查看GitLab CI/CD作业日志
- 监控DNS解析是否正常
- 定期检查备份文件

## 故障排除

### 常见问题

1. **认证失败**
   - 检查API密钥是否正确
   - 确认密钥权限是否足够
   - 验证地域设置是否正确

2. **域名不存在**
   - 确认域名已在腾讯云DNS中添加
   - 检查域名拼写是否正确

3. **配置文件错误**
   - 使用试运行模式验证配置
   - 检查YAML语法是否正确
   - 验证DNS记录格式

4. **API限制**
   - 脚本已内置延迟机制
   - 如遇限制可适当增加延迟时间

### 日志查看

- GitLab CI/CD作业日志：项目 > CI/CD > Jobs
- 本地运行日志：控制台输出
- 备份文件：`backups/` 目录

## 支持的DNS记录类型

| 类型 | 描述 | 示例值 |
|------|------|--------|
| A | IPv4地址记录 | `***********` |
| AAAA | IPv6地址记录 | `2001:db8::1` |
| CNAME | 别名记录 | `target.example.com` |
| MX | 邮件交换记录 | `mail.example.com` |
| TXT | 文本记录 | `v=spf1 include:_spf.example.com ~all` |
| NS | 域名服务器记录 | `ns1.example.com` |

## 联系支持

如遇到问题，请：

1. 查看GitLab CI/CD作业日志
2. 检查配置文件格式
3. 验证API密钥权限
4. 提交Issue到项目仓库
