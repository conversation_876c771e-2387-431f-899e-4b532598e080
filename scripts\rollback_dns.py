#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DNS记录回滚脚本
从备份文件恢复DNS记录
"""

import os
import sys
import json
import click
import time
from typing import Dict, Any, List

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from qcloud_api import QCloudDNSAPI
from utils import setup_logging, format_duration, print_summary


class DNSRollback:
    """DNS回滚管理器"""
    
    def __init__(self, backup_file: str, dry_run: bool = False):
        """
        初始化DNS回滚管理器
        
        Args:
            backup_file: 备份文件路径
            dry_run: 是否为试运行模式
        """
        self.backup_file = backup_file
        self.dry_run = dry_run
        self.logger = setup_logging()
        
        # 初始化腾讯云API
        try:
            self.api = QCloudDNSAPI()
            self.logger.info("腾讯云DNS API初始化成功")
        except Exception as e:
            self.logger.error(f"腾讯云DNS API初始化失败: {e}")
            sys.exit(1)
        
        # 加载备份数据
        self.backup_data = self.load_backup()
    
    def load_backup(self) -> Dict[str, Any]:
        """
        加载备份文件
        
        Returns:
            备份数据
        """
        if not os.path.exists(self.backup_file):
            self.logger.error(f"备份文件不存在: {self.backup_file}")
            sys.exit(1)
        
        try:
            with open(self.backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            self.logger.info(f"备份文件加载成功: {self.backup_file}")
            self.logger.info(f"备份时间: {backup_data.get('backup_time', '未知')}")
            self.logger.info(f"域名: {backup_data.get('domain', '未知')}")
            self.logger.info(f"记录数量: {len(backup_data.get('records', []))}")
            
            return backup_data
            
        except Exception as e:
            self.logger.error(f"备份文件加载失败: {e}")
            sys.exit(1)
    
    def clear_existing_records(self, domain: str) -> List[Dict[str, Any]]:
        """
        清除现有DNS记录
        
        Args:
            domain: 域名
            
        Returns:
            删除结果列表
        """
        results = []
        
        try:
            # 获取当前所有记录
            current_records = self.api.get_record_list(domain)
            
            self.logger.info(f"准备删除 {len(current_records)} 条现有记录")
            
            for record in current_records:
                if self.dry_run:
                    result = {
                        'success': True,
                        'message': f"[试运行] 将删除记录: {record['name']}.{domain} ({record['type']})",
                        'operation': 'dry_run_delete'
                    }
                else:
                    result = self.api.delete_record(domain, record['record_id'])
                    result['operation'] = 'delete'
                    result['record_info'] = f"{record['name']}.{domain} ({record['type']})"
                
                results.append(result)
                
                # 添加延迟避免API限制
                time.sleep(0.3)
            
            return results
            
        except Exception as e:
            self.logger.error(f"清除现有记录失败: {e}")
            return [{
                'success': False,
                'message': f"清除现有记录失败: {e}",
                'operation': 'error'
            }]
    
    def restore_records(self, domain: str, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        恢复DNS记录
        
        Args:
            domain: 域名
            records: 要恢复的记录列表
            
        Returns:
            恢复结果列表
        """
        results = []
        
        self.logger.info(f"准备恢复 {len(records)} 条DNS记录")
        
        for record in records:
            try:
                name = record['name']
                record_type = record['type']
                value = record['value']
                ttl = record.get('ttl', 600)
                mx = record.get('mx')
                weight = record.get('weight')
                
                operation_info = f"{name}.{domain} ({record_type}) -> {value}"
                
                if self.dry_run:
                    result = {
                        'success': True,
                        'message': f"[试运行] 将恢复记录: {operation_info}",
                        'operation': 'dry_run_restore'
                    }
                else:
                    result = self.api.create_record(
                        domain=domain,
                        name=name,
                        record_type=record_type,
                        value=value,
                        ttl=ttl,
                        mx=mx,
                        weight=weight
                    )
                    result['operation'] = 'restore'
                    result['record_info'] = operation_info
                
                results.append(result)
                
                # 添加延迟避免API限制
                time.sleep(0.5)
                
            except Exception as e:
                error_msg = f"恢复记录失败 {record}: {e}"
                self.logger.error(error_msg)
                results.append({
                    'success': False,
                    'message': error_msg,
                    'operation': 'error'
                })
        
        return results
    
    def run(self) -> bool:
        """
        执行DNS记录回滚
        
        Returns:
            是否成功
        """
        start_time = time.time()
        all_results = []
        
        domain = self.backup_data.get('domain')
        records = self.backup_data.get('records', [])
        
        if not domain:
            self.logger.error("备份文件中缺少域名信息")
            return False
        
        self.logger.info(f"开始回滚域名 {domain} 的DNS记录")
        
        if self.dry_run:
            self.logger.info("*** 试运行模式 - 不会实际修改DNS记录 ***")
        
        try:
            # 步骤1：清除现有记录
            self.logger.info("步骤1: 清除现有DNS记录")
            delete_results = self.clear_existing_records(domain)
            all_results.extend(delete_results)
            
            # 等待DNS传播
            if not self.dry_run:
                self.logger.info("等待DNS记录删除传播...")
                time.sleep(10)
            
            # 步骤2：恢复备份记录
            self.logger.info("步骤2: 恢复备份的DNS记录")
            restore_results = self.restore_records(domain, records)
            all_results.extend(restore_results)
            
            # 打印结果摘要
            print_summary(all_results)
            
            # 统计信息
            duration = time.time() - start_time
            success_count = sum(1 for r in all_results if r.get('success', False))
            total_count = len(all_results)
            
            self.logger.info(f"DNS记录回滚完成，耗时: {format_duration(duration)}")
            self.logger.info(f"成功: {success_count}/{total_count}")
            
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"DNS记录回滚执行失败: {e}")
            return False


@click.command()
@click.option('--backup-file', '-b', required=True,
              help='备份文件路径')
@click.option('--dry-run', '-d', is_flag=True,
              help='试运行模式，不实际修改DNS记录')
@click.option('--log-level', '-l', default='INFO',
              type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']),
              help='日志级别')
@click.confirmation_option(prompt='确定要回滚DNS记录吗？这将删除所有现有记录并恢复备份记录。')
def main(backup_file: str, dry_run: bool, log_level: str):
    """
    DNS记录回滚工具
    
    从备份文件恢复DNS记录，会先删除所有现有记录，然后恢复备份的记录。
    """
    # 设置日志级别
    setup_logging(log_level)
    
    # 创建回滚管理器并执行
    rollback = DNSRollback(backup_file, dry_run)
    success = rollback.run()
    
    # 设置退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
