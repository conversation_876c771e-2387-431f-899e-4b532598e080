#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
腾讯云DNS API封装模块
提供DNS记录的增删改查功能
"""

import os
import json
import logging
from typing import List, Dict, Optional, Any
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.dnspod.v20210323 import dnspod_client, models


class QCloudDNSAPI:
    """腾讯云DNS API封装类"""
    
    def __init__(self, secret_id: str = None, secret_key: str = None, region: str = "ap-beijing"):
        """
        初始化腾讯云DNS API客户端
        
        Args:
            secret_id: 腾讯云API密钥ID
            secret_key: 腾讯云API密钥Key
            region: 地域
        """
        self.secret_id = secret_id or os.getenv('TENCENT_SECRET_ID')
        self.secret_key = secret_key or os.getenv('TENCENT_SECRET_KEY')
        self.region = region or os.getenv('TENCENT_REGION', 'ap-beijing')
        
        if not self.secret_id or not self.secret_key:
            raise ValueError("腾讯云API密钥未配置，请设置TENCENT_SECRET_ID和TENCENT_SECRET_KEY环境变量")
        
        # 初始化客户端
        self.cred = credential.Credential(self.secret_id, self.secret_key)
        self.http_profile = HttpProfile()
        self.http_profile.endpoint = "dnspod.tencentcloudapi.com"
        
        self.client_profile = ClientProfile()
        self.client_profile.httpProfile = self.http_profile
        
        self.client = dnspod_client.DnspodClient(self.cred, self.region, self.client_profile)
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
    
    def get_domain_list(self) -> List[Dict[str, Any]]:
        """
        获取域名列表
        
        Returns:
            域名列表
        """
        try:
            req = models.DescribeDomainListRequest()
            resp = self.client.DescribeDomainList(req)
            
            domains = []
            for domain in resp.DomainList:
                domains.append({
                    'domain_id': domain.DomainId,
                    'name': domain.Name,
                    'status': domain.Status,
                    'grade': domain.Grade,
                    'group_id': domain.GroupId,
                    'is_mark': domain.IsMark,
                    'ttl': domain.TTL,
                    'cname_speedup': domain.CnameSpeedup,
                    'remark': domain.Remark,
                    'created_on': domain.CreatedOn,
                    'updated_on': domain.UpdatedOn
                })
            
            self.logger.info(f"获取到 {len(domains)} 个域名")
            return domains
            
        except TencentCloudSDKException as err:
            self.logger.error(f"获取域名列表失败: {err}")
            raise
    
    def get_domain_id(self, domain_name: str) -> Optional[int]:
        """
        根据域名获取域名ID
        
        Args:
            domain_name: 域名
            
        Returns:
            域名ID，如果不存在返回None
        """
        domains = self.get_domain_list()
        for domain in domains:
            if domain['name'] == domain_name:
                return domain['domain_id']
        return None
    
    def get_record_list(self, domain: str, subdomain: str = None, record_type: str = None) -> List[Dict[str, Any]]:
        """
        获取DNS记录列表
        
        Args:
            domain: 域名
            subdomain: 子域名（可选）
            record_type: 记录类型（可选）
            
        Returns:
            DNS记录列表
        """
        try:
            req = models.DescribeRecordListRequest()
            req.Domain = domain
            
            if subdomain:
                req.Subdomain = subdomain
            if record_type:
                req.RecordType = record_type
            
            resp = self.client.DescribeRecordList(req)
            
            records = []
            for record in resp.RecordList:
                records.append({
                    'record_id': record.RecordId,
                    'name': record.Name,
                    'type': record.Type,
                    'value': record.Value,
                    'ttl': record.TTL,
                    'mx': record.MX,
                    'enabled': record.Enabled,
                    'status': record.Status,
                    'weight': record.Weight,
                    'updated_on': record.UpdatedOn
                })
            
            self.logger.info(f"域名 {domain} 获取到 {len(records)} 条DNS记录")
            return records
            
        except TencentCloudSDKException as err:
            self.logger.error(f"获取DNS记录列表失败: {err}")
            raise
    
    def create_record(self, domain: str, name: str, record_type: str, value: str, 
                     ttl: int = 600, mx: int = None, weight: int = None) -> Dict[str, Any]:
        """
        创建DNS记录
        
        Args:
            domain: 域名
            name: 记录名称
            record_type: 记录类型（A, AAAA, CNAME, MX, TXT, NS等）
            value: 记录值
            ttl: TTL值，默认600秒
            mx: MX优先级（仅MX记录需要）
            weight: 权重（可选）
            
        Returns:
            创建结果
        """
        try:
            req = models.CreateRecordRequest()
            req.Domain = domain
            req.RecordType = record_type.upper()
            req.RecordLine = "默认"
            req.Value = value
            req.SubDomain = name
            req.TTL = ttl
            
            if mx is not None and record_type.upper() == 'MX':
                req.MX = mx
            if weight is not None:
                req.Weight = weight
            
            resp = self.client.CreateRecord(req)
            
            result = {
                'record_id': resp.RecordId,
                'success': True,
                'message': f"成功创建DNS记录: {name}.{domain} -> {value}"
            }
            
            self.logger.info(result['message'])
            return result
            
        except TencentCloudSDKException as err:
            error_msg = f"创建DNS记录失败: {err}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(err)
            }
    
    def modify_record(self, domain: str, record_id: int, name: str = None, 
                     record_type: str = None, value: str = None, ttl: int = None,
                     mx: int = None, weight: int = None) -> Dict[str, Any]:
        """
        修改DNS记录
        
        Args:
            domain: 域名
            record_id: 记录ID
            name: 记录名称（可选）
            record_type: 记录类型（可选）
            value: 记录值（可选）
            ttl: TTL值（可选）
            mx: MX优先级（可选）
            weight: 权重（可选）
            
        Returns:
            修改结果
        """
        try:
            req = models.ModifyRecordRequest()
            req.Domain = domain
            req.RecordId = record_id
            req.RecordLine = "默认"
            
            if name is not None:
                req.SubDomain = name
            if record_type is not None:
                req.RecordType = record_type.upper()
            if value is not None:
                req.Value = value
            if ttl is not None:
                req.TTL = ttl
            if mx is not None:
                req.MX = mx
            if weight is not None:
                req.Weight = weight
            
            resp = self.client.ModifyRecord(req)
            
            result = {
                'record_id': resp.RecordId,
                'success': True,
                'message': f"成功修改DNS记录ID: {record_id}"
            }
            
            self.logger.info(result['message'])
            return result
            
        except TencentCloudSDKException as err:
            error_msg = f"修改DNS记录失败: {err}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(err)
            }
    
    def delete_record(self, domain: str, record_id: int) -> Dict[str, Any]:
        """
        删除DNS记录
        
        Args:
            domain: 域名
            record_id: 记录ID
            
        Returns:
            删除结果
        """
        try:
            req = models.DeleteRecordRequest()
            req.Domain = domain
            req.RecordId = record_id
            
            self.client.DeleteRecord(req)
            
            result = {
                'success': True,
                'message': f"成功删除DNS记录ID: {record_id}"
            }
            
            self.logger.info(result['message'])
            return result
            
        except TencentCloudSDKException as err:
            error_msg = f"删除DNS记录失败: {err}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(err)
            }
    
    def find_record(self, domain: str, name: str, record_type: str = None) -> Optional[Dict[str, Any]]:
        """
        查找指定的DNS记录
        
        Args:
            domain: 域名
            name: 记录名称
            record_type: 记录类型（可选）
            
        Returns:
            找到的记录，如果不存在返回None
        """
        records = self.get_record_list(domain, name, record_type)
        
        for record in records:
            if record['name'] == name:
                if record_type is None or record['type'].upper() == record_type.upper():
                    return record
        
        return None
